# Google Analytics Setup Guide

## Overview
Google Analytics has been successfully integrated into your Warriors of Africa Safari web application with GDPR-compliant cookie consent management.

## Current Configuration

### 1. Measurement ID
- **Current ID**: `G-KV2GPTWPDE`
- **Environment Variable**: `VITE_GA_MEASUREMENT_ID`
- **Location**: `.env` file

### 2. Integration Points

#### HTML Head (index.html)
- Google Analytics script is loaded immediately in the HTML head
- Consent mode is initialized with analytics storage denied by default
- IP anonymization and ad personalization are disabled for privacy compliance

#### Dynamic Loading (src/utils/thirdPartyOptimization.ts)
- Additional dynamic loading when user consents to analytics
- Performance monitoring for Google Analytics scripts
- Idle loading to prevent blocking critical resources

#### Cookie Consent (src/components/features/CookieConsent.tsx)
- GDPR-compliant cookie banner
- Granular consent options (Essential, Analytics, Functional, Marketing)
- Proper consent state management in localStorage
- Google Analytics consent mode integration

## Features Implemented

### 1. Privacy-First Approach
- ✅ Consent mode with analytics storage denied by default
- ✅ IP anonymization enabled
- ✅ Ad personalization disabled
- ✅ GDPR-compliant cookie consent banner

### 2. Performance Optimization
- ✅ DNS prefetching for Google Analytics domains
- ✅ Async script loading
- ✅ Idle loading for non-critical analytics
- ✅ Performance monitoring of analytics scripts

### 3. Comprehensive Tracking
- ✅ Automatic page view tracking
- ✅ Web Vitals performance metrics
- ✅ Custom event tracking utilities
- ✅ Safari-specific business events
- ✅ Form submission tracking
- ✅ Error tracking
- ✅ Enhanced ecommerce for bookings

### 4. Developer Experience
- ✅ TypeScript support with proper type definitions
- ✅ React hooks for easy integration (`useAnalytics`, `usePageTracking`, etc.)
- ✅ Utility functions for common tracking scenarios
- ✅ Debug functions to check analytics status

## Usage Examples

### Basic Event Tracking
```typescript
import { useAnalytics } from '../hooks/useAnalytics';

function MyComponent() {
  const { trackInteraction, trackBusiness } = useAnalytics();
  
  const handleButtonClick = () => {
    trackInteraction('click', 'hero_cta_button');
  };
  
  const handleBookingInquiry = () => {
    trackBusiness('booking_inquiry', { safari_type: 'serengeti' });
  };
}
```

### Safari Package Tracking
```typescript
import { useSafariTracking } from '../hooks/useAnalytics';

function SafariCard({ safari }) {
  const { trackPackageView, trackBookingInitiation } = useSafariTracking();
  
  useEffect(() => {
    trackPackageView(safari.id, safari.name, safari.price);
  }, [safari]);
  
  const handleBookNow = () => {
    trackBookingInitiation(safari.id, safari.name, safari.price);
  };
}
```

### Form Tracking
```typescript
import { useFormTracking } from '../hooks/useAnalytics';

function ContactForm() {
  const { trackFormStart, trackFormSubmit, trackFieldInteraction } = useFormTracking('contact_form');
  
  const handleSubmit = async (data) => {
    try {
      await submitForm(data);
      trackFormSubmit(true, { inquiry_type: data.type });
    } catch (error) {
      trackFormSubmit(false, { error: error.message });
    }
  };
}
```

## What's Tracked Automatically

### 1. Page Views
- All route changes in the React app
- Page titles and URLs
- Referrer information

### 2. Performance Metrics
- Core Web Vitals (LCP, INP, CLS, FCP, TTFB)
- Long task detection (>50ms)
- Third-party script performance
- Page load times

### 3. User Consent
- Cookie consent choices
- Analytics opt-in/opt-out events

## Next Steps

### 1. Verify Setup
1. Open browser developer tools
2. Navigate to Network tab
3. Look for requests to `googletagmanager.com`
4. Check Console for any analytics-related errors

### 2. Test Cookie Consent
1. Open the website
2. Accept analytics cookies
3. Verify that `analytics-consent` is set to `'true'` in localStorage
4. Check that Google Analytics requests are being made

### 3. Add Custom Tracking
Use the provided hooks and utilities to add tracking to specific components:
- Safari package views and interactions
- Contact form submissions
- Booking inquiries
- File downloads (brochures, itineraries)
- External link clicks (social media, partner sites)

### 4. Google Analytics Dashboard Setup
1. Log into Google Analytics (analytics.google.com)
2. Navigate to your property (G-KV2GPTWPDE)
3. Set up custom events and conversions
4. Create audience segments for safari interests
5. Set up goals for bookings and inquiries

### 5. Enhanced Ecommerce (Optional)
If you want to track safari bookings as ecommerce transactions:
1. Enable Enhanced Ecommerce in GA4
2. Use `trackPurchase()` function when bookings are completed
3. Set up conversion tracking for booking goals

## Troubleshooting

### Analytics Not Loading
1. Check that `VITE_GA_MEASUREMENT_ID` is set in `.env`
2. Verify user has accepted analytics cookies
3. Check browser console for errors
4. Ensure ad blockers aren't interfering

### Events Not Appearing
1. Events may take 24-48 hours to appear in GA4
2. Use GA4 DebugView for real-time testing
3. Check that `isAnalyticsAvailable()` returns true

### Privacy Compliance
- IP anonymization is enabled by default
- Ad personalization is disabled
- Users can opt-out via cookie preferences
- Consent choices are respected and stored locally

## Files Modified/Created

1. **index.html** - Added Google Analytics script with consent mode
2. **.env** - Added `VITE_GA_MEASUREMENT_ID` environment variable
3. **src/utils/analytics.ts** - New utility functions for tracking
4. **src/hooks/useAnalytics.ts** - New React hooks for easy integration
5. **src/components/features/CookieConsent.tsx** - Enhanced consent management
6. **src/utils/thirdPartyOptimization.ts** - Already had GA integration
7. **src/utils/webVitals.ts** - Already had GA integration

The Google Analytics integration is now complete and ready to use!
