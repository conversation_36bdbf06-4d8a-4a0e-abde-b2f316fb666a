# Firebase Configuration
# Get these values from Firebase Console > Project Settings > General > Your apps
# IMPORTANT: Replace these with your actual Firebase project values
VITE_FIREBASE_API_KEY=your_firebase_api_key_here
VITE_FIREBASE_AUTH_DOMAIN=your_project_id.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your_firebase_project_id
VITE_FIREBASE_STORAGE_BUCKET=your_project_id.firebasestorage.app
VITE_FIREBASE_MESSAGING_SENDER_ID=your_messaging_sender_id
VITE_FIREBASE_APP_ID=your_firebase_app_id
VITE_FIREBASE_MEASUREMENT_ID=your_measurement_id

# Gmail API Configuration
# Get these values from Google Cloud Console after setting up Gmail API
# IMPORTANT: Replace these with your actual Gmail API credentials
VITE_GMAIL_CLIENT_ID=your_gmail_client_id
VITE_GMAIL_CLIENT_SECRET=your_gmail_client_secret
VITE_GMAIL_REFRESH_TOKEN=your_gmail_refresh_token
VITE_GMAIL_ACCESS_TOKEN=your_gmail_access_token
VITE_GMAIL_FROM_EMAIL=<EMAIL>
VITE_GMAIL_TO_EMAIL=<EMAIL>

# Security Notes:
# - Never commit the actual .env file to version control
# - Add .env to your .gitignore file
# - Use different values for development, staging, and production environments
# - Regularly rotate API keys and tokens
# - Use Firebase App Check for additional security in production
